<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{{note_title}}</title>
  <style>
    body {
      margin: 0;
      background: #f4f5f7;
      font-family: 'Inter', sans-serif;
      line-height: 1.6;
    }
    .header {
      background-color: #ffffff;
      padding: 40px 20px 20px;
      font-size: 22px;
      font-weight: 600;
      text-align: center;
      box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      padding: 16px;
    }
    .table-of-contents {
      background: #fff;
      margin-bottom: 24px;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 4px 16px rgba(0,0,0,0.08);
      position: sticky;
      top: 16px;
      z-index: 10;
    }
    .toc-title {
      font-size: 18px;
      font-weight: 600;
      color: #222;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
    }
    .toc-title::before {
      content: "📋";
      margin-right: 8px;
    }
    .toc-item {
      padding: 10px 0;
      font-size: 14px;
      color: #2d72ff;
      cursor: pointer;
      border-bottom: 1px solid #f8f9fa;
      transition: all 0.2s ease;
      position: relative;
    }
    .toc-item:last-child {
      border-bottom: none;
    }
    .toc-item:hover {
      color: #1a5ce6;
      background-color: #f8f9ff;
      padding-left: 8px;
    }
    .toc-item.level-1 {
      font-weight: 500;
      padding-left: 0;
    }
    .toc-item.level-2 {
      padding-left: 20px;
      font-size: 13px;
      color: #5a6c7d;
    }
    .toc-item.level-2:hover {
      color: #2d72ff;
      padding-left: 28px;
    }
    .toc-item.level-3 {
      padding-left: 40px;
      font-size: 12px;
      color: #8a9ba8;
    }
    .toc-item.level-3:hover {
      color: #2d72ff;
      padding-left: 48px;
    }
    .content-area {
      background: #fff;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 4px 16px rgba(0,0,0,0.08);
    }
    .card {
      margin-bottom: 32px;
      padding-bottom: 24px;
      border-bottom: 1px solid #f0f2f5;
    }
    .card:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }
    .card h1, .card h2, .card h3, .card h4 {
      margin: 0 0 16px;
      color: #222;
      scroll-margin-top: 80px;
    }
    .card h1 {
      font-size: 24px;
      font-weight: 700;
      border-bottom: 2px solid #2d72ff;
      padding-bottom: 8px;
    }
    .card h2 {
      font-size: 20px;
      font-weight: 600;
    }
    .card h3 {
      font-size: 18px;
      font-weight: 600;
    }
    .card h4 {
      font-size: 16px;
      font-weight: 500;
    }
    .card p {
      margin: 0 0 16px;
      color: #444;
      font-size: 14px;
      line-height: 1.8;
    }
    .card p:last-child {
      margin-bottom: 0;
    }
  </style>
</head>
<body>
  <div class="header">{{note_title}}</div>

  <div class="container">
    <div class="table-of-contents" id="tableOfContents">
      <div class="toc-title">目录</div>
      <div class="toc-item level-1" data-target="section-1">1. 商业模式解析</div>
      <div class="toc-item level-2" data-target="section-1-1">1.1 营收结构分析</div>
      <div class="toc-item level-2" data-target="section-1-2">1.2 盈利模式探讨</div>
      <div class="toc-item level-3" data-target="section-1-2-1">1.2.1 订阅模式</div>
      <div class="toc-item level-3" data-target="section-1-2-2">1.2.2 增值服务</div>
      <div class="toc-item level-1" data-target="section-2">2. 用户画像与痛点</div>
      <div class="toc-item level-2" data-target="section-2-1">2.1 目标用户分析</div>
      <div class="toc-item level-2" data-target="section-2-2">2.2 用户痛点识别</div>
      <div class="toc-item level-1" data-target="section-3">3. 市场竞争分析</div>
      <div class="toc-item level-2" data-target="section-3-1">3.1 竞品对比</div>
      <div class="toc-item level-2" data-target="section-3-2">3.2 差异化优势</div>
    </div>

    <div class="content-area">
      <div class="card" id="section-1">
        <h1>商业模式解析</h1>
        <p>本章节深入分析SaaS平台的商业模式，探讨其营收结构和盈利模式的核心要素。</p>
      </div>

      <div class="card" id="section-1-1">
        <h2>营收结构分析</h2>
        <p>SaaS平台的营收结构主要包括订阅费用、增值服务费用以及合作分成等多个维度。通过对这些收入来源的详细分析，我们可以更好地理解平台的盈利能力。</p>
      </div>

      <div class="card" id="section-1-2">
        <h2>盈利模式探讨</h2>
        <p>深入探讨SaaS平台的核心盈利模式，分析不同模式的优劣势和适用场景。</p>
      </div>

      <div class="card" id="section-1-2-1">
        <h3>订阅模式</h3>
        <p>订阅模式是SaaS平台最主要的盈利方式，通过提供稳定的服务获得持续的收入流。这种模式的优势在于收入的可预测性和用户粘性的建立。</p>
      </div>

      <div class="card" id="section-1-2-2">
        <h3>增值服务</h3>
        <p>在基础订阅服务之外，平台还可以通过提供定制化服务、高级功能、专业咨询等增值服务来获得额外收入。</p>
      </div>

      <div class="card" id="section-2">
        <h1>用户画像与痛点</h1>
        <p>基于市场调研和用户行为数据，构建详细的用户画像，识别核心痛点和需求。</p>
      </div>

      <div class="card" id="section-2-1">
        <h2>目标用户分析</h2>
        <p>通过数据分析和用户访谈，我们识别出平台的核心用户群体主要包括中小企业管理者、创业者以及专业服务提供商。</p>
      </div>

      <div class="card" id="section-2-2">
        <h2>用户痛点识别</h2>
        <p>用户在使用传统解决方案时面临的主要痛点包括：成本高昂、操作复杂、数据孤岛、缺乏定制化等问题。</p>
      </div>

      <div class="card" id="section-3">
        <h1>市场竞争分析</h1>
        <p>全面分析市场竞争格局，识别主要竞争对手和差异化机会。</p>
      </div>

      <div class="card" id="section-3-1">
        <h2>竞品对比</h2>
        <p>对市场上主要竞争产品进行功能、价格、用户体验等多维度对比分析，找出各自的优势和不足。</p>
      </div>

      <div class="card" id="section-3-2">
        <h2>差异化优势</h2>
        <p>基于竞品分析结果，明确我们产品的核心差异化优势和市场定位策略。</p>
      </div>
    </div>
  </div>
  <script data-id="toc-navigation">
    // 目录点击跳转功能
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('toc-item')) {
        const targetId = e.target.getAttribute('data-target');
        const targetElement = document.getElementById(targetId);
        if (targetElement) {
          // 平滑滚动到目标位置，考虑固定目录的高度
          const tocHeight = document.querySelector('.table-of-contents').offsetHeight;
          const targetPosition = targetElement.offsetTop - tocHeight - 20;

          window.scrollTo({
            top: targetPosition,
            behavior: 'smooth'
          });

          // 添加高亮效果
          targetElement.style.backgroundColor = '#f8f9ff';
          targetElement.style.transition = 'background-color 0.3s ease';

          setTimeout(() => {
            targetElement.style.backgroundColor = '';
          }, 2000);
        }
      }
    });

    // 滚动时高亮当前章节对应的目录项
    function highlightCurrentSection() {
      const sections = document.querySelectorAll('.card[id^="section-"]');
      const tocItems = document.querySelectorAll('.toc-item');
      const tocHeight = document.querySelector('.table-of-contents').offsetHeight;
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

      let currentSection = null;

      sections.forEach(section => {
        const sectionTop = section.offsetTop - tocHeight - 50;
        if (scrollTop >= sectionTop) {
          currentSection = section;
        }
      });

      // 清除所有高亮
      tocItems.forEach(item => {
        item.style.fontWeight = '';
        item.style.color = '';
      });

      // 高亮当前章节
      if (currentSection) {
        const currentId = currentSection.getAttribute('id');
        const currentTocItem = document.querySelector(`[data-target="${currentId}"]`);
        if (currentTocItem) {
          currentTocItem.style.fontWeight = '600';
          currentTocItem.style.color = '#1a5ce6';
        }
      }
    }

    // 监听滚动事件
    let ticking = false;
    window.addEventListener('scroll', () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          highlightCurrentSection();
          ticking = false;
        });
        ticking = true;
      }
    });

    // 页面加载完成后初始化高亮
    document.addEventListener('DOMContentLoaded', highlightCurrentSection);
  </script>
</body>
</html>