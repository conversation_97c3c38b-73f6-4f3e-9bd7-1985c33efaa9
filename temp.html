<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>商业笔记滑动 Tab</title>
  <style>
    body {
      margin: 0;
      background: #f4f5f7;
      height: 100vh;
      font-family: 'Inter', sans-serif;
      display: flex;
      flex-direction: column;
    }
    .header {
      background-color: #ffffff;
      padding-top: 40px;
      padding-bottom: 0;
      font-size: 22px;
      font-weight: 600;
      text-align: center;
    }
    .tab-bar {
      display: flex;
      overflow-x: auto;
      background-color: #fff;
      border-bottom: 1px solid #eee;
      position: relative;
    }
    .tab-item {
      padding: 12px 20px;
      white-space: nowrap;
      font-size: 15px;
      font-weight: 500;
      color: #666;
      cursor: pointer;
      position: relative;
    }
    .tab-item.active {
      color: #2d72ff;
    }
    .tab-item.active::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 24px;
      height: 2px;
      background-color: #2d72ff;
      border-radius: 1px;
    }
    .table-of-contents {
      background: #fff;
      margin-bottom: 16px;
      border-radius: 8px;
      padding: 16px 20px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    }
    .toc-title {
      font-size: 16px;
      font-weight: 600;
      color: #222;
      margin-bottom: 12px;
    }
    .toc-item {
      padding: 8px 0;
      font-size: 14px;
      color: #2d72ff;
      cursor: pointer;
      border-bottom: 1px solid #f5f5f5;
      transition: color 0.2s;
    }
    .toc-item:last-child {
      border-bottom: none;
    }
    .toc-item:hover {
      color: #1a5ce6;
    }
    .scroll-area {
      flex: 1;
      overflow: hidden;
      position: relative;
    }
    .tab-content {
      width: 100%;
      height: 100%;
      overflow-y: auto;
      display: none;
      padding: 16px;
      box-sizing: border-box;
      -webkit-overflow-scrolling: touch;
    }
    .tab-content.active {
      display: block;
    }
    .card {
      background: #fff;
      margin-bottom: 16px;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    }
    .card h3 {
      margin: 0 0 10px;
      font-size: 18px;
      font-weight: 600;
      color: #222;
    }
    .card p {
      margin: 0;
      color: #444;
      font-size: 14px;
      line-height: 1.6;
    }
  </style>
</head>
<body>
    <div class="header">商业笔记</div>
    <div class="tab-bar" id="tabBar">
      <div class="tab-item active">概览</div>
      <div class="tab-item">技术</div>
      <div class="tab-item">市场</div>
      <div class="tab-item">运营</div>
      <div class="tab-item">设计</div>
    </div>
    <div class="scroll-area" id="scrollArea">
      <div class="tab-content active">
        <div class="table-of-contents" id="tableOfContents">
          <div class="toc-title">目录</div>
          <div class="toc-item" data-target="card-0">商业模式解析</div>
          <div class="toc-item" data-target="card-1">用户画像与痛点</div>
        </div>
        <div class="card" id="card-0">
          <h3>商业模式解析</h3>
          <p>介绍了SaaS平台的营收结构，包括订阅、增值服务及合作分成模型。</p>
        </div>
        <div class="card" id="card-1">
          <h3>用户画像与痛点</h3>
          <p>基于调研数据，识别目标用户行为特征和痛点。</p>
        </div>
      </div>
      <div class="tab-content">
        <div class="card"><h3>敬请期待</h3><p>内容正在筹备中，请稍后再来。</p></div>
      </div>
      <div class="tab-content">
        <div class="card"><h3>敬请期待</h3><p>内容正在筹备中，请稍后再来。</p></div>
      </div>
      <div class="tab-content">
        <div class="card"><h3>敬请期待</h3><p>内容正在筹备中，请稍后再来。</p></div>
      </div>
      <div class="tab-content">
        <div class="card"><h3>敬请期待</h3><p>内容正在筹备中，请稍后再来。</p></div>
      </div>
    </div>
  <script data-id="tab-behavior">
    const tabs = document.querySelectorAll('.tab-item');
    const contents = document.querySelectorAll('.tab-content');
    const scrollArea = document.getElementById('scrollArea');

    function activateTab(index) {
      tabs.forEach(t => t.classList.remove('active'));
      contents.forEach(c => c.classList.remove('active'));
      tabs[index].classList.add('active');
      contents[index].classList.add('active');
    }

    tabs.forEach((tab, index) => {
      tab.addEventListener('click', () => activateTab(index));
    });

    // 目录点击跳转功能
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('toc-item')) {
        const targetId = e.target.getAttribute('data-target');
        const targetElement = document.getElementById(targetId);
        if (targetElement) {
          targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }
    });

    let startX = 0;
    let startY = 0;
    scrollArea.addEventListener('touchstart', e => {
      startX = e.touches[0].clientX;
      startY = e.touches[0].clientY;
    });
    scrollArea.addEventListener('touchend', e => {
      const endX = e.changedTouches[0].clientX;
      const endY = e.changedTouches[0].clientY;
      const diffX = endX - startX;
      const diffY = endY - startY;

      // 只有当水平滑动距离大于垂直滑动距离时才切换tab
      if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
        const currentIndex = [...tabs].findIndex(t => t.classList.contains('active'));
        if (diffX < -50 && currentIndex < tabs.length - 1) activateTab(currentIndex + 1);
        else if (diffX > 50 && currentIndex > 0) activateTab(currentIndex - 1);
      }
    });
  </script>
</body>
</html>